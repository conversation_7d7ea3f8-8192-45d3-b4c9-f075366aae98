#!/usr/bin/env python3
"""
Battery Type Analyzer

This script analyzes battery types from various data sources and creates a comprehensive
battery type mapping for all batteries that appear in the battery lifecycle timelines.

Data Sources:
- battery_lifecycle_timelines.csv (unique batteries to analyze)
- hv_repair_2025-06-02b.csv (repair events with battery types)
- Fahrzeug Batterie Zuordnung-with-batteryid.xlsx (vehicle-battery assignments)
- Fahrzeug Batterie Zuordnung-with-first-zulassung.xlsx (vehicle-battery assignments with registration dates)
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import sys
from typing import Dict, List, Set, Optional
import warnings

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("analyze_battery_timeline.log"),
    ],
)
logger = logging.getLogger(__name__)


class BatteryTypeAnalyzer:
    """Analyze battery types from multiple data sources."""

    def __init__(self):
        # Data containers
        self.battery_lifecycle_df = None
        self.hv_repair_df = None
        self.vehicle_battery_with_id_df = None
        self.vehicle_battery_with_zulassung_df = None
        
        # Processing results
        self.unique_batteries = set()
        self.battery_info = {}  # battery_id -> {'battery_type': str, 'source': str}
        
        self.stats = {
            "total_batteries": 0,
            "batteries_with_type": 0,
            "batteries_without_type": 0,
            "sources": {
                "hv_repair": 0,
                "vehicle_assignment_with_id": 0,
                "vehicle_assignment_with_zulassung": 0
            }
        }

    def load_data(self):
        """Load all data files."""
        logger.info("Loading data files...")

        # Load battery lifecycle timelines to get unique batteries
        logger.info("Loading battery lifecycle timelines...")
        self.battery_lifecycle_df = pd.read_csv("battery_lifecycle_timelines.csv")
        logger.info(f"Loaded {len(self.battery_lifecycle_df)} battery lifecycle records")

        # Load HV repair data
        logger.info("Loading HV repair data...")
        self.hv_repair_df = pd.read_csv("hv_repair_2025-06-02b.csv")
        logger.info(f"Loaded {len(self.hv_repair_df)} HV repair records")

        # Load vehicle battery assignment files
        logger.info("Loading vehicle battery assignment files...")
        self.vehicle_battery_with_id_df = pd.read_excel("Fahrzeug Batterie Zuordnung-with-batteryid.xlsx")
        self.vehicle_battery_with_zulassung_df = pd.read_excel("Fahrzeug Batterie Zuordnung-with-first-zulassung.xlsx")
        
        logger.info(f"Loaded {len(self.vehicle_battery_with_id_df)} records from batteryid file")
        logger.info(f"Loaded {len(self.vehicle_battery_with_zulassung_df)} records from zulassung file")

    def clean_data(self):
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        # Clean HV repair data
        for col in ["battery_id_old", "battery_id_new", "battery_type_old", "battery_type_new"]:
            if col in self.hv_repair_df.columns:
                self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
                self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                    ["nan", "", " ", "None", "NaN"], None
                )

        # Clean vehicle battery assignment data
        for df in [self.vehicle_battery_with_id_df, self.vehicle_battery_with_zulassung_df]:
            for col in ["HV Master", "HV Slave", "Batterietyp"]:
                if col in df.columns:
                    df[col] = df[col].astype(str)
                    df[col] = df[col].replace(["nan", "", " ", "None", "NaN"], None)

        logger.info("Data cleaning completed")

    def extract_unique_batteries(self):
        """Extract unique batteries from battery lifecycle timelines."""
        logger.info("Extracting unique batteries from lifecycle timelines...")
        
        self.unique_batteries = set(self.battery_lifecycle_df['battery_id'].astype(str).unique())
        self.stats["total_batteries"] = len(self.unique_batteries)
        
        logger.info(f"Found {len(self.unique_batteries)} unique batteries in lifecycle timelines")

    def build_battery_info(self):
        """Build battery information from all data sources."""
        logger.info("Building battery information from all sources...")
        
        # Initialize battery_info for all unique batteries
        for battery_id in self.unique_batteries:
            self.battery_info[battery_id] = {
                'battery_type': None,
                'source': None,
                'notes': []
            }
        
        # Process HV repair data
        self._process_hv_repair_data()
        
        # Process vehicle assignment files
        self._process_vehicle_assignment_data()
        
        # Generate final statistics
        self._generate_statistics()

    def _process_hv_repair_data(self):
        """Process HV repair data to extract battery types."""
        logger.info("Processing HV repair data for battery types...")
        
        for idx, row in self.hv_repair_df.iterrows():
            # Process old battery
            battery_id_old = row.get("battery_id_old")
            battery_type_old = row.get("battery_type_old")
            
            if (battery_id_old and battery_id_old in self.unique_batteries and 
                battery_type_old and pd.notna(battery_type_old)):
                
                if self.battery_info[battery_id_old]['battery_type'] is None:
                    self.battery_info[battery_id_old]['battery_type'] = battery_type_old
                    self.battery_info[battery_id_old]['source'] = 'hv_repair_old'
                    self.stats["sources"]["hv_repair"] += 1
                elif self.battery_info[battery_id_old]['battery_type'] != battery_type_old:
                    self.battery_info[battery_id_old]['notes'].append(
                        f"Type conflict in hv_repair: existing={self.battery_info[battery_id_old]['battery_type']}, new={battery_type_old}"
                    )
            
            # Process new battery
            battery_id_new = row.get("battery_id_new")
            battery_type_new = row.get("battery_type_new")
            
            if (battery_id_new and battery_id_new in self.unique_batteries and 
                battery_type_new and pd.notna(battery_type_new)):
                
                if self.battery_info[battery_id_new]['battery_type'] is None:
                    self.battery_info[battery_id_new]['battery_type'] = battery_type_new
                    self.battery_info[battery_id_new]['source'] = 'hv_repair_new'
                    self.stats["sources"]["hv_repair"] += 1
                elif self.battery_info[battery_id_new]['battery_type'] != battery_type_new:
                    self.battery_info[battery_id_new]['notes'].append(
                        f"Type conflict in hv_repair: existing={self.battery_info[battery_id_new]['battery_type']}, new={battery_type_new}"
                    )

    def _process_vehicle_assignment_data(self):
        """Process vehicle assignment files to extract battery types."""
        logger.info("Processing vehicle assignment files for battery types...")
        
        # Process both files with same structure
        files_info = [
            (self.vehicle_battery_with_id_df, "vehicle_assignment_with_id"),
            (self.vehicle_battery_with_zulassung_df, "vehicle_assignment_with_zulassung")
        ]
        
        for df, source_name in files_info:
            logger.info(f"Processing {source_name}...")
            
            for idx, row in df.iterrows():
                battery_type = row.get("Batterietyp")
                
                if not battery_type or pd.isna(battery_type):
                    continue
                
                # Process HV Master
                hv_master = row.get("HV Master")
                if hv_master and hv_master in self.unique_batteries:
                    self._update_battery_type(hv_master, battery_type, source_name)
                
                # Process HV Slave
                hv_slave = row.get("HV Slave")
                if hv_slave and hv_slave in self.unique_batteries:
                    self._update_battery_type(hv_slave, battery_type, source_name)

    def _update_battery_type(self, battery_id: str, battery_type: str, source: str):
        """Update battery type information with conflict detection."""
        if self.battery_info[battery_id]['battery_type'] is None:
            self.battery_info[battery_id]['battery_type'] = battery_type
            self.battery_info[battery_id]['source'] = source
            
            if "with_id" in source:
                self.stats["sources"]["vehicle_assignment_with_id"] += 1
            else:
                self.stats["sources"]["vehicle_assignment_with_zulassung"] += 1
                
        elif self.battery_info[battery_id]['battery_type'] != battery_type:
            self.battery_info[battery_id]['notes'].append(
                f"Type conflict in {source}: existing={self.battery_info[battery_id]['battery_type']}, new={battery_type}"
            )

    def _generate_statistics(self):
        """Generate final statistics."""
        batteries_with_type = sum(1 for info in self.battery_info.values() if info['battery_type'] is not None)
        batteries_without_type = len(self.battery_info) - batteries_with_type
        
        self.stats["batteries_with_type"] = batteries_with_type
        self.stats["batteries_without_type"] = batteries_without_type
        
        logger.info(f"Battery type analysis complete:")
        logger.info(f"  Total batteries: {self.stats['total_batteries']}")
        logger.info(f"  Batteries with type: {batteries_with_type}")
        logger.info(f"  Batteries without type: {batteries_without_type}")
        logger.info(f"  Sources breakdown: {self.stats['sources']}")

    def generate_output(self):
        """Generate battery type CSV output."""
        logger.info("Generating battery type output...")
        
        # Prepare output data
        output_data = []
        
        for battery_id in sorted(self.unique_batteries):
            info = self.battery_info[battery_id]
            battery_type = info['battery_type']
            
            # Add note for batteries without type
            if battery_type is None:
                note = "Type not found in any data source"
            else:
                note = ""
            
            # Add conflict notes if any
            if info['notes']:
                if note:
                    note += "; " + "; ".join(info['notes'])
                else:
                    note = "; ".join(info['notes'])
            
            output_data.append({
                'battery_id': battery_id,
                'battery_type': battery_type if battery_type else '',
                'source': info['source'] if info['source'] else '',
                'notes': note
            })
        
        # Create DataFrame and save to CSV
        output_df = pd.DataFrame(output_data)
        output_filename = "battery_type.csv"
        output_df.to_csv(output_filename, index=False)
        
        logger.info(f"Saved battery type data to {output_filename}")
        
        # Generate statistics file
        stats_filename = "battery_type_analysis_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Type Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {self.stats['total_batteries']}\n")
            f.write(f"Batteries with Type: {self.stats['batteries_with_type']}\n")
            f.write(f"Batteries without Type: {self.stats['batteries_without_type']}\n")
            f.write(f"Coverage: {self.stats['batteries_with_type']/self.stats['total_batteries']*100:.1f}%\n\n")
            
            f.write("Sources Breakdown:\n")
            for source, count in self.stats['sources'].items():
                f.write(f"  {source}: {count}\n")
            
            f.write(f"\nOutput Files:\n")
            f.write(f"  Battery types: {output_filename}\n")
            f.write(f"  Statistics: {stats_filename}\n")
        
        logger.info(f"Saved statistics to {stats_filename}")
        
        return output_filename, stats_filename

    def run(self):
        """Run the complete battery type analysis."""
        logger.info("Starting battery type analysis...")
        
        try:
            self.load_data()
            self.clean_data()
            self.extract_unique_batteries()
            self.build_battery_info()
            output_file, stats_file = self.generate_output()
            
            logger.info("Battery type analysis completed successfully!")
            logger.info(f"Results saved to: {output_file}")
            logger.info(f"Statistics saved to: {stats_file}")
            
            return output_file, stats_file
            
        except Exception as e:
            logger.error(f"Error during battery type analysis: {e}")
            raise


def main():
    """Main function to run the battery type analyzer."""
    analyzer = BatteryTypeAnalyzer()
    analyzer.run()


if __name__ == "__main__":
    main()
